# 10000101 活期存入交易

## 概述

活期存入交易的完整代码实现，包括每一个组件的具体逻辑，调用链路的完整梳理，以及详细的执行流程。

## 1. 交易入口

### 1.1 ICore10000101接口定义

**关键信息**:

- 服务URL: `/rb/fin/current/dep`
- 服务编码: `MbsdCore-1000-0101`
- 支持存款类型: 活期存折账户、活期账户、对公活期账户

### 1.2 Core10000101服务实现

## 2. 核心业务流程

### 2.1 Core10000101Flow业务流程

#### 2.1.1 identifyAcctStd账户模型确认

**关键信息**:

- 获取主账户信息， 获取产品信息
- 客户号一致性校验
- 黑白名单检查
- 多币种账户和外币子账户处理
- MCA多币种账户处理
- 构建账户标准上下文

```java
// 1、获取最终的账户信息
// 2、应用标识校验
// 3、获取客户信息和产品信息
// 4、构建业务上下文
// 5、追缴账户特殊处理
// 6、设置交易类型
```

#### 2.1.2 execute核心执行方法

## 3. Gravity组件流程

Gravity流程包含以下组件（按执行顺序）：

```mermaid
graph TB
    %% 定义样式类
    classDef startStyle fill:#e8f5e8,stroke:#4caf50,stroke-width:3px,color:#1b5e20,font-weight:bold
    classDef processStyle fill:#e3f2fd,stroke:#2196f3,stroke-width:2px,color:#0d47a1,font-weight:500
    classDef decisionStyle fill:#fff3e0,stroke:#ff9800,stroke-width:2px,color:#e65100,font-weight:500
    classDef errorStyle fill:#ffebee,stroke:#f44336,stroke-width:2px,color:#b71c1c,font-weight:500
    classDef successStyle fill:#e8f5e8,stroke:#4caf50,stroke-width:3px,color:#1b5e20,font-weight:bold
    

A[开始]:::startStyle
B[结束]:::successStyle
ClientCheck[客户校验流程组-客户基本校验BusiClientCheckGroup.checkClientBasis组件]:::processStyle
AcctCheck[账户校验流程组-账户基本校验BusiAcctCheckGroup.checkAcctBasis组件]:::processStyle
cond{开立检查}:::decisionStyle
checkRes[交易校验流程组-分户限制检查BusiTranCheckGroup.checkAllRestraints组件]
Core10000101Stria[活期存入Core10000101Stria.execute组件]
Core10000101Stria0[活期存入自动开外币子账户Core10000101Stria.execute0组件]

A --> ClientCheck
ClientCheck --> AcctCheck
AcctCheck --> cond
cond -->|OpenFlag = N| checkRes
cond -->|OpenFlag = Y| Core10000101Stria0
checkRes --> Core10000101Stria
Core10000101Stria --> B
Core10000101Stria0 --> B
```



### 3.1 客户校验流程组 - 客户基本校验

**BusiClientCheckGroup.checkClientBasis 执行逻辑**:

1. 记录客户校验开始日志
2. 调用`rbClientCheckComponent.checkClientCommon`方法执行通用客户校验
3. 校验内容包括客户状态、客户类型等基本信息验证
4. 记录客户校验结束日志

### 3.2 账户校验流程组 - 账户基本校验

**BusiAcctCheckGroup.checkAcctBasis 执行逻辑**:

1. 记录账户校验开始日志
2. 检查业务参数上下文中的`openFlag`，如果是开户操作("Y")则跳过校验
3. 如果不是开户操作，执行账户通用检查
4. 根据`isIndividual`标识分别执行个人账户检查或对公账户检查
5. 记录账户校验结束日志

### 3.3 交易校验流程组 - 分户限制检查

**BusiTranCheckGroup.checkAllRestraints 执行逻辑**:

1. 记录分户限制检查开始日志
2. 设置生效日期，默认为当前营业日期
3. 获取或构建交易定义对象
4. 构建限制检查模型，包括账户信息、生效日期、交易定义
5. 通过工厂模式获取限制检查业务实例
6. 执行ALL类型的限制检查
7. 如果有错误则抛出业务异常
8. 执行渠道控制检查
9. 特殊账户状态检查（暂收账户等）
10. 记录检查结束日志

### 3.4 活期存入

#### 3.4.1 Core10000101Stria.execute

**详细代码分析**:

**执行逻辑**:

1. 获取交易定义
2. 获取账户标准模型
3. 检查账户类型，定期账户不能存款
4. 检查账户状态，暂收账户不能存款
5. 检查交易金额
6. 检查交易机构币种
7. 现金来源和用途检查
8. 核心交易处理逻辑
9. 更正交易检查
10. 构建账户交易输入模型
11. 组装交易模型
12. 构建异步交易输入
13. 处理服务费明细
14. 调用TAE记账引擎
15. 返回结果

#### 3.4.2 TAE处理方法processTae

**执行逻辑**:

1. 计算当前TAE交易类型枚举
2. 判断账户币种和交易币种是否一致
    1. 跨币种交易处理
    2. 同币种交易处理
3. 构建TAE引擎输入
4. 发送给TAE记账引擎处理

### 3.5 自动开外币子账户

**执行逻辑**:
1. 记录开始日志
2. 调用`iMbAcctMaintApplicationBg.openForeSubAcct`方法自动开立外币子账户
3. 传入基础账号、交易币种、交易明细等参数
4. 记录结束日志
5. 返回空的输出对象





## 4. TEA回调

```mermaid
graph TD
    A[TAE异步记账请求] --> B[AsynFinancialImpl]
    B --> D[AsynFinancialFlow.execute]
   
    D --> N{tranCode类型判断}
    N -->|普通活期贷记:NormalCurrentCret| O[AsynNormalCurrentCretFlow.process]
    N -->|其他类型| P[其他Flow处理]
 
    O --> Q[初始化和参数设置]
    Q --> R[设置上下文信息]
    R --> W{交易类型判断}
    
    W -->|活期借记服务| X[currentTranService.debtTransaction]
    W -->|活期存入服务| Y[currentTranService.cretTransaction]
    


    Y --> BB[intTaxPorcess利息税处理]
    
    X --> CC[借记后处理]

    BB --> DD[贷记后处理]
    CC --> EE[交易状态更新]
    DD --> EE
    
    EE --> FF[后置处理]
    FF --> PP[构建AsynFinancialOut]
    PP --> QQ[返回结果]
    
    style A fill:#90EE90
    style QQ fill:#FFB6C1
    style O fill:#87CEEB
    style X fill:#DDA0DD
    style Y fill:#F0E68C

```



### 4.1 IAsynFinancial 接口定义

**关键信息**:

- 服务URL: `/rb/fin/asyn/financial
- 存款异步记账统一入口，TAE回调统一入口具体实现由交易场景独立实现

### 4.2 AsynFinancialFlow服务实现

1. 设置子流水号
2. 通过工厂获取对应的TAE流程处理器
3. 转换请求参数
4. 执行具体业务流程
5. 账户检查处理

