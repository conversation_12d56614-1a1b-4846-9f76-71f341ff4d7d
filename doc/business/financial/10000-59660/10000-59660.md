

# 10000-59660 - 通用记账

## 1. 概述

### 1.1 基本信息

- **接口编号**: **********
- **接口名称**: 通用记账
- **服务路径**: `/rb/fin/channel/common/account`

### 1.2 接口功能描述

提供给外围通用计账功能，包括：

- 客户账记账
- 科目计账  
- 费用通用记账处理
- 凭证处理（分为凭证尾箱处理和国债凭证挂失解挂）
- 支持现金交易更新现金尾箱
- 按顺序记账
- 支持多借多贷记账

### 1.2.3 核心实现

1. **统一记账入口**: 为所有外围系统提供统一的记账接口
2. **多场景支持**: 支持客户账、科目、费用、凭证等多种记账场景
3. **高度灵活性**: 支持多借多贷、跨币种、现金交易等复杂场景
4. **强一致性**: 通过TAE引擎保证记账数据的强一致性

## 2 数据流转

### 2.1 数据流转流程图

```mermaid
sequenceDiagram
  participant Client as 外围系统
  participant Controller as Core**********
  participant Flow as Core**********Flow
  participant AbstractFlow as AbstractRbBusiFlow
  participant Stria as Core**********Stria
  participant TAE as TAE交易引擎
  participant TB as TB系统
  participant PF as PF系统
  participant CIF as CIF系统
  participant DB as 数据库

  Client ->> Controller: HTTP请求(Core**********In)
  Note over Client, Controller: 记账数组
  Controller ->> Flow: ExecutorFlow.startFlow()
  Note over Controller, Flow: 启动Flow流程
  Flow ->> AbstractFlow: executeTx()
  Note over Flow, AbstractFlow: 事务执行入口
  AbstractFlow ->> Flow: identifyAcctStd()
  Flow -->> AbstractFlow: RbAcctStdContext
  Note over AbstractFlow: 识别账户标准上下文
  AbstractFlow ->> AbstractFlow: commBusiChck()
  Note over AbstractFlow: 业务前置检查
  AbstractFlow ->> AbstractFlow: controlCheckGroup.allCheck()
  Note over AbstractFlow: 渠道限制检查
  AbstractFlow ->> AbstractFlow: controlCheckGroup.northboundCheck()
  Note over AbstractFlow: 北向系统检查
  AbstractFlow ->> AbstractFlow: executeGravity()
  Note over AbstractFlow: 调用executeGravity方法
  AbstractFlow ->> Stria: FlowDiagramExecutor.executorGravityDiagram()
  Note over AbstractFlow, Stria: Gravity组件自动路由
  Stria ->> Stria: 事件类型判断
  Note over Stria: 判断DEBT/CRET事件类型
  Stria ->> Stria: 记账数组处理
  Note over Stria: 处理glArray记账条目
  alt 客户账记账
    Stria ->> CIF: 获取客户信息
    CIF -->> Stria: 客户基本信息
    Stria ->> DB: 查询账户信息
    DB -->> Stria: 账户标准模型
  else 科目记账
    Stria ->> DB: 查询科目信息
    DB -->> Stria: 科目配置信息
  else 费用记账
    Stria ->> DB: 查询费用配置
    DB -->> Stria: 费用计算规则
  else 凭证处理
    Stria ->> DB: 查询凭证状态
    DB -->> Stria: 凭证信息
  end
  alt 跨币种交易
    Stria ->> PF: getExchangeRate()
    PF -->> Stria: 汇率信息
    Note over Stria, PF: 汇率转换处理
  end
  alt 现金交易
    Stria ->> TB: lmBoxCheckForDay()
    TB -->> Stria: 尾箱检查结果
    Note over Stria, TB: 现金尾箱限额检查
  end
  Stria ->> Stria: 构建TAE交易
  Note over Stria: 创建TaeAcctMaintradesRow和TaeAcctSubtradesRow
  Stria ->> TAE: TaeRpc.transactionFoOrder()
  Note over Stria, TAE: 调用TAE交易引擎
  TAE ->> DB: 执行记账操作
  Note over TAE, DB: 更新账户余额、交易历史等
  DB -->> TAE: 记账结果
  TAE -->> Stria: SettleEngineOut
  Stria ->> Stria: 结果处理
  Note over Stria: 处理透支检查等后续逻辑
  Stria -->> AbstractFlow: Core**********Out
  AbstractFlow ->> AbstractFlow: dealOut()
  Note over AbstractFlow: 输出结果处理
  AbstractFlow -->> Flow: 处理结果
  Flow -->> Controller: Core**********Out
  Controller -->> Client: HTTP响应
  Note over Controller, Client: 返回记账结果




```

### 2.2 重点功能

#### 2.2.1 多借多贷记账支持

- 支持多笔记账条目
- 每个条目可以是借记或贷记

#### 2.2.2 跨币种处理

- 自动进行汇率转换
- 支持多币种混合记账

#### 2.2.3 现金交易处理

- 支持现金尾箱更新
- 进行现金限额检查
- 调用TB系统进行尾箱限额验证

#### 2.2.4 凭证处理

- 支持凭证尾箱处理
- 通过`RbVoucherCheckComponent`进行凭证检查

#### 2.2.5 挂账核销

- 支持挂账核销交易
- 通过`IGlHangWriteOffTranService`处理核销逻辑
- 检查账户挂账核销标志

## 3 功能检查

### 3.1通用记账状态检查

| 序号 | 描述                                          | 类型     | 备注         |
| ---- | --------------------------------------------- | -------- | ------------ |
| 1    | 数据非空校验                                  | 技术参数 | 参数校验     |
| 2    | 记账交易数据校验，不可以超过20个交易同时记账  | 技术参数 | 参数校验     |
| 3    | 是否旧卡标识，如果是旧卡则交易卡号替换为新卡. |          | 废弃         |
| 4    | 对公久悬户，不允许交易                        | 产品指标 | 产品指标校验 |

### 3.2 跨境资金池校验 - 待定

| 序号 | 描述                     | 类型 | 备注         |
| ---- | ------------------------ | ---- | ------------ |
| 1    | 校验逻辑双向跨境资金池   |      | 主子账户检查 |
| 2    | 校验逻辑本外币跨境资金池 |      | 主子账户检查 |
| 3    | 校验主，子账户协议信息   |      | 主子账户检查 |
| 4    | 转账账户校验             |      |              |

### 3.3 记账检查

#### 3.3.1 参数检查

| 序号 | 描述                                           | 类型     | 备注                                      |
| ---- | ---------------------------------------------- | -------- | ----------------------------------------- |
| 1    | 是否旧卡进行， 进行账号重新设置                |          | 废弃                                      |
| 2    | 同账户检查控制，记账双方不能为同一客户的账户   | 账户     | 与双向跨境资金池主子账户检查互斥          |
| 3    | 账户信息和科目代码不能同时上送检查             | 通用检查 |                                           |
| 4    | 账户信息和科目代码不能同时为空，且未映射内部户 | 通用检查 |                                           |
| 5    | 扣税交易，跳过交易检查                         |          | 调整为TranCode关联参数控制：              |
| 6    | 交易类型合法性检查                             | 通用检查 |                                           |
| 7    | 法透检查                                       |          | 调整为TranCode关联参数控制： 法透检查标识 |

#### 3.3.2 控制检查

| 序号 | 描述                                                         | 类型 | 备注                                         |
| ---- | ------------------------------------------------------------ | ---- | -------------------------------------------- |
| 1    | 渠道控制检查 - 只检查借方账户                                |      | 调整为TranCode关联参数控制：                 |
| 2    | 扣税交易类型跳过校验                                         |      | 调整为TranCode关联参数控制：                 |
| 3    | 内部账户校验 - 内部账户柜面跨行记账许可校验检查              |      | 产品指标检查                                 |
| 4    | 手续费收取方式检验                                           |      | 产品指标检查                                 |
| 5    | 币种检查                                                     |      | 调整为TranCode关联参数控制：是否跨币种检查   |
| 6    | 北向通校验                                                   |      | 待定                                         |
| 7    | 内部户账户映射校验 - 当账户需要内部户映射时，查询内部户映射关系，映射BaseAcctNo |      | 调整为TranCode关联参数控制：内部账户控制检查 |
| 8    | 是否旧卡标识，如果是旧卡则交易卡号替换为新卡.                |      | 废弃                                         |
| 9    | 账户类型校验，定期不允许做相关记账交易                       |      |                                              |
| 10   | 交易金额检查， 进行交易金额累计，当存在跨币种时，需要进行等值金额计算后在累计 |      |                                              |
| 11   | 金额校验， 非内部账户交易金额不允许为负数                    |      |                                              |
| 12   | 法透检查，当账户余额大于交易金额时， 不进行法透检查          |      |                                              |
| 13   | 系统标识，及系统来源控制                                     |      |                                              |
| 14   | 机构额度校验                                                 |      |                                              |
| 15   | 客户校验，客户基本信息校验检查                               |      |                                              |
| 16   | 科目校验检查                                                 |      |                                              |
| 17   | 协议检查: 法人透支协议检查                                   |      |                                              |
| 18   | 黑名单检查-待确认                                            |      |                                              |
| 19   | 现金尾箱控制检查： 现金存入增加柜员现金尾箱额度检查          |      |                                              |
| 20   | 尾箱限额检查 - 借方检查                                      |      |                                              |
| 21   | 账户检查： 久悬户控制检查 - 借方检查                         |      |                                              |
| 22   | 对手信息检查                                                 |      |                                              |
| 23   | 限制检查： 账户控制检查，客户控制检查 - 借方检查             |      |                                              |
| 24   | 挂销账校验检查，销账时销帐编号不允许为空，不支持内部账挂帐，非挂销账账户不允许销账，校验交易方向 |      |                                              |
| 25   | 凭证校验：凭证类型，凭证类型和交易类型现金转账标识检查， 票据信息和账号信息检查，票据日期与交易日关系检查，票据状态检查 |      |                                              |
| 26   | 是否冲正控制校验                                             |      |                                              |

## 4、TAE回调

### 4.1 交易码统计

| 交易码                          | 描述                   | 备注 |
| ------------------------------- | ---------------------- | ---- |
| `NORMAL_CURRENT_DEBT`           | 普通活期借记           |      |
| `NORMAL_CURRENT_CRET`           | 普通活期贷记           |      |
| `RECORD_TB_TRAN_HIST `          |                        |      |
| `NORMAL_COMMON_ACCOUNT_NOACCT`  | 通用记账无账号（现金） |      |
| `CASH_TAILBOX_UPDATE`           | 现金尾箱更新           |      |
| `NORMAL_CURRENT_CHARGE`         | 手续费收取             |      |
| `NORMAL_CURRENT_CM_SETTEL_CRET` | 现金管理结息           |      |
| `NORMAL_COMMON_ACCOUNT`         | 通用记账               |      |



交易，（借贷方，通用检查），事件，  -- 检查

现金存入 -- 借方科目

活期转账 -- 账户

内部户提现 -- 贷：客户账，借：内部户



备注： 暂未涉及跨币种结售汇流程相关校验检查
