package com.dcits.ensemble.rb.business.bc.component.acct.tran.process;

import com.dcits.comet.commons.Context;
import com.dcits.comet.util.json.JSONUtil;
import com.dcits.ensemble.rb.business.api.component.acct.tran.IRbBusiTranProcess;
import com.dcits.ensemble.rb.business.bc.unit.acct.transaction.business.TransactionUtil;
import com.dcits.ensemble.rb.business.bc.unit.acct.transaction.business.secbal.NarrativeDefService;
import com.dcits.ensemble.rb.business.common.constant.RbEventEnum;
import com.dcits.ensemble.rb.business.common.rpc.CifRpc;
import com.dcits.ensemble.rb.business.common.rpc.ProductRpc;
import com.dcits.ensemble.rb.business.common.util.tae.GenerateTaeTradesUtil;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementCsl;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbTranDef;
import com.dcits.ensemble.rb.business.model.acct.SourceModuleEnum;
import com.dcits.ensemble.rb.business.model.agr.AgreementTypeEnum;
import com.dcits.ensemble.rb.business.model.cm.BaseEventModel;
import com.dcits.ensemble.rb.business.model.cm.common.RbAcctStandardModel;
import com.dcits.ensemble.rb.business.model.cm.common.RbProduct;
import com.dcits.ensemble.rb.business.model.cm.common.TranCodeDict;
import com.dcits.ensemble.rb.business.model.cm.restful.cif.CifBaseInfo;
import com.dcits.ensemble.rb.business.model.cm.restful.rb.AsynNormalCurrentCretIn;
import com.dcits.ensemble.rb.business.model.cm.restful.tae.TaeAcctMaintradesRow;
import com.dcits.ensemble.rb.business.model.cm.restful.tae.TaeAcctSubtradesRow;
import com.dcits.ensemble.rb.business.model.cm.transaction.bal.AcctTransactionInModel;
import com.dcits.ensemble.rb.business.model.interest.model.RbBusiEnum;
import com.dcits.ensemble.util.BusiUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Service
@Slf4j
public class BusiNormalCurrentCretProcess implements IRbBusiTranProcess {

    @Override
    public TranCodeDict getProcess() {
        return TranCodeDict.NORMAL_CURRENT_CRET;
    }

    @Override
    public List<TaeAcctSubtradesRow> process(BaseEventModel inModel, TaeAcctMaintradesRow maintradesRow) {
        List<TaeAcctSubtradesRow> subtradesRowList = new ArrayList<>();
        // 获取账户交易入参模型
        AcctTransactionInModel model = (AcctTransactionInModel) inModel;
        // 获取账户模型
        RbAcctStandardModel acctStandardModel = model.getRbAcctStandardModel();
        // 获取交易类型定义
        RbTranDef rbTranDef = model.getRbTranDef();

        //贷方子流水组织
        //如果实际上送了实际金额，交易金额为实际金额
        if (BusiUtil.isNotNull(model.getCashSignAmt())){
            model.setTranAmt(model.getActualAmt());
        }
        AsynNormalCurrentCretIn asynNormalCurrentCretIn = new AsynNormalCurrentCretIn(model);
        String jsonData = JSONUtil.toJsonStr(asynNormalCurrentCretIn);
        TaeAcctSubtradesRow subCretRow = GenerateTaeTradesUtil.createRbSingleSubRow(
                maintradesRow.getMainId(), model.getTranAmt(), acctStandardModel, rbTranDef, TranCodeDict.NORMAL_CURRENT_CRET.toString(), jsonData);
        subtradesRowList.add(subCretRow);

        return subtradesRowList;
    }

    @Override
    public BaseEventModel buildModel(BaseEventModel inModel) {
        // 获取账户交易入参模型
        AcctTransactionInModel model = (AcctTransactionInModel) inModel;

        // 账户信息
        RbAcctStandardModel wtdStandard = model.getRbAcctStandardModel();
        RbAcctStandardModel depStandard = model.getOthRbAcctStandardModel();
        //华兴法透需求： 如果借方签约了法透的话  那么贷方则进行白名单限额检查
        if (BusiUtil.isNotNull(wtdStandard) && model.getOverdraftFlag()) {
            if (BusiUtil.isNotNull(model.getOdCheckFlag())) {
                model.setOdCheckFlag(model.getOdCheckFlag());
            }
            model.setIsOverMonthSeasonOd(model.getIsOverMonthSeasonOd());
            model.setOdLimitCretCheckFlag("Y");
            model.setDrBaseAcctNo(wtdStandard.getBaseAcctNo());
            model.setDrClientNo(wtdStandard.getClientNo());
            model.setOthAcctDesc(model.getOthAcctName());
        }
        RbProduct rbProduct = ProductRpc.getRbProduct(depStandard.getProdType(), RbEventEnum.CRET.toString(),depStandard.getCompany());
        model.setRbProduct(rbProduct);

        RbTranDef othRbTranDef = TransactionUtil.getMbTranDef(model.getRbTranDef().getOthTranType());
        //活期存入(存入账户为主账户BaseAcctNo，支取账户为OthBaseAcctNo)
        AcctTransactionInModel acctTransactionInModelCret = new AcctTransactionInModel(depStandard, othRbTranDef, model.getTranAmt());
        /*查询贷记方客户信息*/
        acctTransactionInModelCret.setDocumentId(depStandard.getDocumentId());
        acctTransactionInModelCret.setDocumentType(depStandard.getDocumentType());
        acctTransactionInModelCret.setCardNo(depStandard.getCardNo());
        acctTransactionInModelCret.setOdLimitCretCheckFlag(model.getOdLimitCretCheckFlag());
        acctTransactionInModelCret.setIsOverMonthSeasonOd(model.getIsOverMonthSeasonOd());
        acctTransactionInModelCret.setDrBaseAcctNo(model.getDrBaseAcctNo());
        acctTransactionInModelCret.setDrClientNo(model.getDrClientNo());
        acctTransactionInModelCret.setOdCheckFlag(model.getOdCheckFlag());
        acctTransactionInModelCret.setOthAcctDesc(model.getOthAcctDesc());
        if (BusiUtil.isNull(acctTransactionInModelCret.getCardNo())) {
            acctTransactionInModelCret.setCardNo(model.getOthCardNo());
        }
        acctTransactionInModelCret.setMediumType(model.getMediumType());
        acctTransactionInModelCret.setMediumFlag(model.getMediumFlag());
        acctTransactionInModelCret.setEffectDate(model.getEffectDate());
        acctTransactionInModelCret.setOthBaseAcctNo(wtdStandard.getActualAcctNo());
        acctTransactionInModelCret.setOthProdType(wtdStandard.getProdType());
        acctTransactionInModelCret.setOthAcctCcy(wtdStandard.getCcy());
        acctTransactionInModelCret.setOthAcctSeqNo(wtdStandard.getAcctSeqNo());
        acctTransactionInModelCret.setOthInternalKey(wtdStandard.getInternalKey());
        acctTransactionInModelCret.setOthCardNo(wtdStandard.getCardNo());
        acctTransactionInModelCret.setOthTranType(model.getTranType());
        acctTransactionInModelCret.setOthBranch(wtdStandard.getAcctBranch());
        acctTransactionInModelCret.setOthDocumentType(wtdStandard.getDocumentType());
        acctTransactionInModelCret.setOthDocumentId(wtdStandard.getDocumentId());
        acctTransactionInModelCret.setOthAcctName(wtdStandard.getAcctName());
        acctTransactionInModelCret.setReference(model.getReference());
        acctTransactionInModelCret.setTranBatchNo(model.getTranBatchNo());
        //lixue modify  清算改造，利率市场化直接根据BatchGlTranHist进行清算,修改交易流水表中的交易机构
        // 非联机交易场景下，保持mbTranHist中的借贷方的交易机构一致，账户机构为对应账号机构。
        if (Context.getInstance().isBatch()) {
            acctTransactionInModelCret.setTranBranch(wtdStandard.getAcctBranch());
        }
        //narrative
        acctTransactionInModelCret.setNarrative(model.getNarrative());
        acctTransactionInModelCret.setNarrativeCode(NarrativeDefService.getNarrativeCodeDef("ZZ", BusiUtil.getMessageByKey("MG0266")));
        acctTransactionInModelCret.setTranNote(model.getTranNote());
        //对方户名
        if (BusiUtil.isNull(acctTransactionInModelCret.getOdLimitCretCheckFlag())) {
            acctTransactionInModelCret.setOthAcctDesc(wtdStandard.getAcctName());
        } else {
            acctTransactionInModelCret.setOthAcctDesc(model.getOthAcctDesc());
        }
        //查询客户信息
        String internalFlag="";
        if(BusiUtil.isEquals(depStandard.getSourceModule(), SourceModuleEnum.RB.getCode())&&BusiUtil.isEquals(depStandard.getIsIndividual(), RbBusiEnum.YES)){
            internalFlag=RbBusiEnum.NO;
        }else {
            internalFlag=RbBusiEnum.YES;
        }
        CifBaseInfo depCifBaseInfo = CifRpc.getCifBaseInfoInternal(depStandard.getClientNo(),internalFlag);
        acctTransactionInModelCret.setCifBaseInfo(depCifBaseInfo);
        acctTransactionInModelCret.setTranCcy(model.getTranCcy());
        //挂销账信息
        if (BusiUtil.isNotNull(model.getHangOperateType1())) {
            acctTransactionInModelCret.setHangSeqNo(model.getHangSeqNo());
            acctTransactionInModelCret.setHangOperateType1(model.getHangOperateType1());
            acctTransactionInModelCret.setHangEndDate(model.getHangEndDate());
            acctTransactionInModelCret.setHangTerm(model.getHangTerm());
            acctTransactionInModelCret.setHangDealType(model.getHangDealType());
        }
        //合并吉林：是否绑定账户互转标志
        acctTransactionInModelCret.setBindSettleFlag(model.getBindSettleFlag());
        acctTransactionInModelCret.setOthRbAcctStandardModel(wtdStandard);

        return acctTransactionInModelCret;
    }
}
