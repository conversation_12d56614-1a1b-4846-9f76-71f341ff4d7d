package com.dcits.ensemble.rb.business.model.acct;

/**
 * The enum BusiCheck type enum.
 */
public enum CheckTypeEnum {

    /**
     * ACCT_BASIS: 账户基本检查
     */
    ACCT_BASIS,
    /**
     * ACCT_BASIS: 对手账户基本检查
     */
    OTH_ACCT_BASIS,
    /**
     * CLIENT_BASIS: 客户基本检查
     */
    CLIENT_BASIS,
    /**
     * AGREEMENT: 协议检查
     */
    AGREEMENT,
    /**
     * DOCUMENT: 证件检查
     */
    DOCUMENT,
    /**
     * RESTRAINT: 限制检查
     */
    RESTRAINT;

    private Integer order;

    private Integer getOrder() {
        return order;
    }

    CheckTypeEnum() {

    }

    /**
     * 业务检查对应优先级
     * 按顺序排序后 取优先级高的
     *
     * @param order order
     */
    CheckTypeEnum(Integer order) {
        this.order = order;
    }

    /**
     * Gets order.
     *
     * @param checkType the checkType type
     * @return the order
     */
    public static Integer getOrder(String checkType) {
        return valueOf(checkType).getOrder();
    }

}
