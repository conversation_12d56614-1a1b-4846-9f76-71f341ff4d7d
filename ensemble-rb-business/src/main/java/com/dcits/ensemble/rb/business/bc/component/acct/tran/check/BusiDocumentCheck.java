package com.dcits.ensemble.rb.business.bc.component.acct.tran.check;

import com.dcits.ensemble.rb.business.api.component.acct.tran.IRbBusiTranCheck;
import com.dcits.ensemble.rb.business.model.acct.CheckTypeEnum;
import com.dcits.ensemble.rb.business.model.acct.RbTranCheckModel;
import com.dcits.ensemble.rb.business.model.cm.BaseEventModel;
import com.dcits.ensemble.rb.business.model.cm.common.RbAcctStandardModel;
import com.dcits.ensemble.rb.business.model.cm.transaction.bal.AcctTransactionInModel;
import com.dcits.ensemble.util.BusiUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class BusiDocumentCheck implements IRbBusiTranCheck {

    @Override
    public CheckTypeEnum getCheckClass() {
        return CheckTypeEnum.DOCUMENT;
    }

    @Override
    public void check(BaseEventModel inModel, RbTranCheckModel checkModel) {
        AcctTransactionInModel model = (AcctTransactionInModel) inModel;

        RbAcctStandardModel acctStdModel = model.getRbAcctStandardModel();
        String documentId = model.getDocumentId();
        String documentType = model.getDocumentType();
        //如果上送了证件类型或证件号码
        if(BusiUtil.isNotNull(documentType) || BusiUtil.isNotNull(documentId)){
            //只上送了证件号
            if (BusiUtil.isNotNull(documentId) && BusiUtil.isNull(documentType)){
                //请上送证件类型
                throw BusiUtil.createBusinessException("RB9600");
            }else {
                //上送了证件类型未上送证件号码
                if (BusiUtil.isNull(documentId)){
                    if (!BusiUtil.isEquals(acctStdModel.getDocumentType(), documentType)){
                        throw BusiUtil.createBusinessException("RB6157");
                    }
                }else {
                    //上送了证件类型和证件号码
                    if (!(BusiUtil.isEquals(acctStdModel.getDocumentType(), documentType) && BusiUtil.isEquals(acctStdModel.getDocumentId(), documentId))){
                        throw BusiUtil.createBusinessException("RB6157");
                    }
                }
            }
        }
    }
}
