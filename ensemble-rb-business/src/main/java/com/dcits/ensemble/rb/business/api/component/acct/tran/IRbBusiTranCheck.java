package com.dcits.ensemble.rb.business.api.component.acct.tran;

import com.dcits.ensemble.rb.business.model.acct.CheckTypeEnum;
import com.dcits.ensemble.rb.business.model.acct.RbTranCheckModel;
import com.dcits.ensemble.rb.business.model.cm.BaseEventModel;

/**
 * The interface Rb acct tran check.
 */
public interface IRbBusiTranCheck {

    /**
     * get check class
     * @return check class
     */
    CheckTypeEnum getCheckClass();


    /**
     * process check
     * @param inModel inModel
     */
    void check(BaseEventModel inModel, RbTranCheckModel checkModel);
}
