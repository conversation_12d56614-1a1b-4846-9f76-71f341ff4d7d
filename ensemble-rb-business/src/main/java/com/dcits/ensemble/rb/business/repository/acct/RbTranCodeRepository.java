package com.dcits.ensemble.rb.business.repository.acct;

import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.rb.business.dbmodel.RbTranCode;
import com.dcits.ensemble.rb.business.dbmodel.RbTranProcess;
import com.dcits.ensemble.repository.BusinessRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@BusiUnit
@Slf4j
public class RbTranCodeRepository extends BusinessRepository {

    public RbTranCode selectTranCodeInfo(String flowId, String tranCode, String channel, String company) {
        Map<String, Object> param = new HashMap<>(16);
        param.put("flowId", flowId);
        param.put("tranCode", tranCode);
        param.put("channel",channel);
        param.put("company",company);
        return daoSupport.selectOne(RbTranCode.class.getName() + ".selectOne",  param);
    }

    public List<RbTranCode> selectTranProcessList(RbTranCode rbTranCode) {
        Map<String, Object> param = new HashMap<>(16);
        param.put("flowId", rbTranCode.getFlowId());
        param.put("tranCode", rbTranCode.getTranCode());
        param.put("channel",rbTranCode.getTranId());
        param.put("company",rbTranCode.getCompany());
        return daoSupport.selectList(RbTranCode.class.getName() + ".selectList",  param);
    }


}
