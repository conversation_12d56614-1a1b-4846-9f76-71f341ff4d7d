package com.dcits.ensemble.rb.business.api.component.acct.tran;

import com.dcits.ensemble.rb.business.model.acct.CheckTypeEnum;
import com.dcits.ensemble.util.BusiUtil;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * The type busi check flow Factory
 */
@Component
public class RbBusiTranCheckFactory implements ApplicationContextAware {

    private static Map<CheckTypeEnum, IRbBusiTranCheck> busiTranCheckMap;

    /**
     * Get IRbBusiTranCheck
     * @param type type
     * @return IRbBusiTranCheck
     */
    public static IRbBusiTranCheck getBusiTranCheck(CheckTypeEnum type) {
        return busiTranCheckMap.get(type);
    }

    /**
     * Get ALl BusiTranCheck
     * @return Map
     */
    public static Map<CheckTypeEnum, IRbBusiTranCheck> getAllBusiTranCheck() {
        return busiTranCheckMap;
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        Map<String, IRbBusiTranCheck> map = applicationContext.getBeansOfType(IRbBusiTranCheck.class);
        busiTranCheckMap = new HashMap<>();
        if (BusiUtil.isNotNull(map)) {
            map.forEach((k, v) -> {
                busiTranCheckMap.put(v.getCheckClass(), v);
            });
        }
    }
}
