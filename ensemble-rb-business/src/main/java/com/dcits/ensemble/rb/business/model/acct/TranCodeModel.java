package com.dcits.ensemble.rb.business.model.acct;


import com.dcits.ensemble.rb.business.model.cm.BaseEventModel;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class TranCodeModel extends BaseEventModel {

    /**
     * 交易ID
     */
    private String tranId;
    /**
     * 流程ID
     */
    private String flowId;
    /**
     * 交易分类
     */
    private String tranCode;
    /**
     * 交易分类描述
     */
    private String tranCodeDesc;
    /**
     * 渠道
     */
    private String channel;
    /**
     * 交易时间
     */
    private Date tranDate;
    /**
     * 最后修改时间
     */
    private Date lastChangeDate;

    /**
     * 最后修改柜员
     */
    private String lastChangeUserId;
    /**
     * 法人
     */
    private String company;
    /**
     * 交易时间戳
     */
    private String tranTimestamp;

    /**
     * 交易执行结合
     */
    private List<RbTranProcessModel>  tranProcessModels;

    /**
     * 交易检查集合
     */
    private List<RbTranCheckModel> tranCheckModels;

}
