package com.dcits.ensemble.rb.business.model.acct;

import com.dcits.ensemble.rb.business.model.cm.BaseEventModel;
import lombok.Data;

import java.util.Date;

@Data
public class RbTranCheckModel extends BaseEventModel {

    /**
     * 附属物品编号
     */
    private String attrId;
    /**
     * 检查标识
     */
    private String checkInd;
    /**
     * 状态
     */
    private String status;
    /**
     * 尾箱更新标志
     */
    private String updTailboxFlag;
    /**
     * 交易时间戳
     */
    private String tranTimestamp;
    /**
     * 最后修改柜员
     */
    private String lastChangeUserId;
    /**
     * 传输代码
     */
    private String tranCode;
    /**
     * 属性规则
     */
    private String attrRule;
    /**
     * 属性值
     */
    private String attrValue;
    /**
     * 交易日期
     */
    private Date tranDate;
    /**
     * 借贷标志
     */
    private String crDrInd;
    /**
     * 序号
     */
    private String seqNo;
    /**
     * 最后修改日期
     */
    private Date lastChangeDate;
    /**
     * 流程总ID
     */
    private String processId;
    /**
     * 法人
     */
    private String company;

    /**
     * 交易ID
     */
    private String tranId;

    /**
     * 参数数据类型
     */
    private String attrType;

    /**
     * 现金交易
     */
    private String cashTranFlag;

}
