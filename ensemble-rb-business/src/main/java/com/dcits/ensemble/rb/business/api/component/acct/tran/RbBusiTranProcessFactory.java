package com.dcits.ensemble.rb.business.api.component.acct.tran;

import com.dcits.ensemble.rb.business.model.cm.common.TranCodeDict;
import com.dcits.ensemble.util.BusiUtil;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * The Busi Tran Process Factory
 */
@Component
public class RbBusiTranProcessFactory implements ApplicationContextAware {

    private static Map<TranCodeDict, IRbBusiTranProcess> busiTranProcessMap;

    /**
     * get IRbBusiTranProcess
     * @param tranCode tranCode
     * @return IRbBusiTranProcess
     */
    public static IRbBusiTranProcess getBusiTranProcess(TranCodeDict tranCode) {
        return busiTranProcessMap.get(tranCode);
    }

    /**
     * Get All BusiTranProcess
     * @return Map
     */
    public static Map<TranCodeDict, IRbBusiTranProcess> getAllBusiTranProcess() {
        return busiTranProcessMap;
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        Map<String, IRbBusiTranProcess> map = applicationContext.getBeansOfType(IRbBusiTranProcess.class);
        busiTranProcessMap = new HashMap<>();
        if (BusiUtil.isNotNull(map)) {
            map.forEach((k, v) -> {
                busiTranProcessMap.put(v.getProcess(), v);
            });
        }
    }
}
