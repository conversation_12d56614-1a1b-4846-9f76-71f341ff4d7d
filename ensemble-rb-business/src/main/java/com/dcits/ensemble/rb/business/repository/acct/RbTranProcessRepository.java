package com.dcits.ensemble.rb.business.repository.acct;

import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.rb.business.dbmodel.RbTranProcess;
import com.dcits.ensemble.repository.BusinessRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@BusiUnit
@Slf4j
public class RbTranProcessRepository extends BusinessRepository {

    public RbTranProcess selectTranProcessInfo(String processId, String tranId, String tranCode, String company) {
        Map<String, Object> param = new HashMap<>(16);
        param.put("processId", processId);
        param.put("tranCode", tranCode);
        param.put("tranId",tranId);
        param.put("company",company);
        return daoSupport.selectOne(RbTranProcess.class.getName() + ".selectOne",  param);
    }

    public List<RbTranProcess> selectTranProcessList(RbTranProcess rbTranProcess) {
        Map<String, Object> param = new HashMap<>(16);
        param.put("processId", rbTranProcess.getProcessId());
        param.put("tranCode", rbTranProcess.getTranCode());
        param.put("tranId",rbTranProcess.getTranId());
        param.put("company",rbTranProcess.getCompany());
        return daoSupport.selectList(RbTranProcess.class.getName() + ".selectList",  param);
    }


}
