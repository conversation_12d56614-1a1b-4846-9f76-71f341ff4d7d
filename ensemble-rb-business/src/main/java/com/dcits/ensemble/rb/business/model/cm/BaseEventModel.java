package com.dcits.ensemble.rb.business.model.cm;

import com.dcits.ensemble.rb.business.model.cm.common.BaseModel;
import com.dcits.ensemble.rb.business.model.cm.common.RbProduct;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class BaseEventModel extends BaseModel {

   /**
    * 产品类型
    */
   private String prodType;
   /**
    * 事件类型
    */
   private String eventType;
   /**
    * 业务控制标识
    */
   private String flowControl;
   /**
    * 产品信息
    */
   private RbProduct rbProduct;
   /**
    * 交易分类
    * */
   private String tranCode ;
   /**
    * 流程ID
    * */
   private String flowId ;

}
