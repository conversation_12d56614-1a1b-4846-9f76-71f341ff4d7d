package com.dcits.ensemble.rb.business.common.acct.tran;

import com.dcits.comet.commons.Context;
import com.dcits.comet.rpc.api.model.head.SysHead;
import com.dcits.ensemble.base.data.EnsRequest;
import com.dcits.ensemble.rb.business.api.component.acct.tran.IRbBusiTranCheck;
import com.dcits.ensemble.rb.business.api.component.acct.tran.IRbBusiTranProcess;
import com.dcits.ensemble.rb.business.api.component.acct.tran.RbBusiTranCheckFactory;
import com.dcits.ensemble.rb.business.api.component.acct.tran.RbBusiTranProcessFactory;
import com.dcits.ensemble.rb.business.bc.unit.acct.transaction.business.TransactionUtil;
import com.dcits.ensemble.rb.business.common.rpc.TaeRpc;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbTranDef;
import com.dcits.ensemble.rb.business.model.acct.CheckTypeEnum;
import com.dcits.ensemble.rb.business.model.acct.RbTranCheckModel;
import com.dcits.ensemble.rb.business.model.acct.RbTranProcessModel;
import com.dcits.ensemble.rb.business.model.acct.TranCodeModel;
import com.dcits.ensemble.rb.business.model.cm.BaseEventModel;
import com.dcits.ensemble.rb.business.model.cm.common.TranCodeDict;
import com.dcits.ensemble.rb.business.model.cm.restful.tae.SettleEngineIn;
import com.dcits.ensemble.rb.business.model.cm.restful.tae.SettleEngineOut;
import com.dcits.ensemble.rb.business.model.cm.restful.tae.TaeAcctMaintradesRow;
import com.dcits.ensemble.rb.business.model.cm.restful.tae.TaeAcctSubtradesRow;
import com.dcits.ensemble.rb.business.model.cm.transaction.bal.AcctTransactionInModel;
import com.dcits.ensemble.rb.business.model.cm.transaction.bal.RbAcctTransactionOutModel;
import com.dcits.ensemble.util.BusiUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Triple;
import org.springframework.beans.factory.annotation.Autowired;


import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public abstract class BusiTranEngine<In extends BaseEventModel, Out extends BaseEventModel> {

    @Autowired
    private RbTranCodeOperate rbTranCodeOperate;

    public In tranInModel;
    public TaeAcctMaintradesRow maintradesRow;
    public List<TaeAcctSubtradesRow> subtradesRows;

    public Out applyTrans (EnsRequest req) {
        // 构建交易请求参数模型 - 请求入参映射
        buildTransactionInModel(req);

        BaseEventModel out = applyCalc(tranInModel);

        // 执行TAE调
        this.execTae();

        return (Out) out;
    }

    private Out applyCalc(In inModel) {
        // 构建交易业务执行模型
        TranCodeModel tranCodeModel = buildTranCodeModel(inModel);
        // 初始化TAE流水
        this.buildTranTaeRow(inModel, tranCodeModel);

        RbAcctTransactionOutModel out = new RbAcctTransactionOutModel();
        // 构建执行数据：
        // 1、通用检查
        // 2、执行前置检查
        // 3、执行处理
        Triple<List<RbTranCheckModel>, Map<String, List<RbTranCheckModel>>, List<RbTranProcessModel>> proceeTriple = this.assembleProcess(tranCodeModel);

        // 通用检查: 按照顺序进行
        if (BusiUtil.isNotNull(proceeTriple.getLeft())) {
            List<RbTranCheckModel> commonCheckList = proceeTriple.getLeft();
            commonCheckList.forEach(check -> {
                IRbBusiTranCheck rbBusiTranCheck = RbBusiTranCheckFactory.getBusiTranCheck(CheckTypeEnum.valueOf(check.getAttrId()));
                rbBusiTranCheck.check(inModel, check);
            });
        }

        // 业务扩展检查
        this.check(inModel);
        // 业务扩展执行逻辑
        this.process(inModel);

        if (BusiUtil.isNotNull(proceeTriple.getRight())) {
            List<RbTranProcessModel> busiprocessList = proceeTriple.getRight();
            Map<String, List<RbTranCheckModel>> processCheckMap = proceeTriple.getMiddle();

            for (RbTranProcessModel process : busiprocessList) {
                // 执行前置检查
                List<RbTranCheckModel> processCheckList = processCheckMap.get(process.getProcessId());
                if (BusiUtil.isNotNull(processCheckList)) {
                   this.beforProcessCheck(processCheckList, inModel);
                }
                // 执行
                IRbBusiTranProcess rbBusiTranProcess = RbBusiTranProcessFactory.getBusiTranProcess(TranCodeDict.valueOf(process.getProcessId()));
                // 进行执行请求模型构建
                BaseEventModel processModel = rbBusiTranProcess.buildModel(inModel);
                subtradesRows.addAll(rbBusiTranProcess.process(processModel, maintradesRow));
            }
        }

        out.setTaeAcctMaintradesRow(maintradesRow);
        out.setTaeAcctSubtradesRow(subtradesRows);
        return (Out) out;
    }

    /**
     * 执行前置借贷方检查
     * @param processCheckList processCheckList
     * @param inModel inModel
     */
    private void beforProcessCheck(List<RbTranCheckModel> processCheckList, In inModel) {
        AcctTransactionInModel model = (AcctTransactionInModel) inModel;
        // 获取交易类型
        RbTranDef rbTranDef = model.getRbTranDef();
        // 对手账户交易类型
        RbTranDef othRbTranDef;
        if (BusiUtil.isNotNull(rbTranDef.getOthTranType())) {
            othRbTranDef = TransactionUtil.getMbTranDef(rbTranDef.getOthTranType());
        } else {
            othRbTranDef = null;
        }

        // 借贷方检查: 执行前置检查
        if (BusiUtil.isNotNull(rbTranDef)) {
            processCheckList.stream()
                    .filter(s -> BusiUtil.isEquals(rbTranDef.getCrDrInd(), s.getCheckInd()))
                    .forEach(processCheck -> {
                        IRbBusiTranCheck rbBusiTranCheck = RbBusiTranCheckFactory.getBusiTranCheck(CheckTypeEnum.valueOf(processCheck.getAttrId()));
                        rbBusiTranCheck.check(inModel, processCheck);
                    });
        }
        if (BusiUtil.isNotNull(othRbTranDef)) {
            processCheckList.stream()
                    .filter(s -> BusiUtil.isEquals(othRbTranDef.getCrDrInd(), s.getCheckInd()))
                    .forEach(processCheck -> {
                        IRbBusiTranCheck rbBusiTranCheck = RbBusiTranCheckFactory.getBusiTranCheck(CheckTypeEnum.valueOf(processCheck.getAttrId()));
                        rbBusiTranCheck.check(inModel, processCheck);
                    });
        }
    }

    /**
     * 构建执行数据
     * @param tranCodeModel tranCodeModel
     * @return Triple
     */
    private Triple<List<RbTranCheckModel>, Map<String, List<RbTranCheckModel>>, List<RbTranProcessModel>> assembleProcess(TranCodeModel tranCodeModel) {
        List<RbTranCheckModel> commonCheckList = new ArrayList<>();
        Map<String, List<RbTranCheckModel>> processCheckMap = new HashMap<>();
        List<RbTranProcessModel> tranProcessModels = new ArrayList<>();

        if (BusiUtil.isNotNull(tranCodeModel.getTranCheckModels())) {
            // 获取通用检查项：升序排序
            commonCheckList = tranCodeModel.getTranCheckModels().stream()
                    .filter(s -> BusiUtil.isNull(s.getProcessId()))
                    .filter(s -> BusiUtil.isEquals(s.getCheckInd(), "ALL"))
                    .sorted(Comparator.comparing(RbTranCheckModel::getSeqNo))
                    .collect(Collectors.toList());

            // 按照ProcessId进行分组，然后在进行升序排序
            processCheckMap = tranCodeModel.getTranCheckModels().stream()
                    .filter(s -> BusiUtil.isNotNull(s.getProcessId()))
                    .filter(s -> BusiUtil.isNotEquals(s.getCheckInd(), "ALL"))
                    .collect(
                            Collectors.groupingBy(
                                    RbTranCheckModel::getProcessId,
                                    Collectors.collectingAndThen(
                                            Collectors.toList(), list -> {
                                                list.sort(Comparator.comparing(RbTranCheckModel::getSeqNo));
                                                return list;
                                            })
                            )
                    );
        }
        if (BusiUtil.isNotNull(tranCodeModel.getTranProcessModels())) {
            // 交易执行项: 升序排序
            tranProcessModels = tranCodeModel.getTranProcessModels().stream().sorted(Comparator.comparing(RbTranProcessModel::getSeqNo)).collect(Collectors.toList());
        }

        return Triple.of(commonCheckList, processCheckMap, tranProcessModels);
    }

    /**
     * 构建主流水信息
     * @param inModel inModel
     * @param tranCodeModel tranCodeModel
     * @return TaeAcctMaintradesRow
     */
    private void buildTranTaeRow(In inModel, TranCodeModel tranCodeModel) {
        if (BusiUtil.isNull(maintradesRow) && BusiUtil.isNull(subtradesRows)) {
            initTaeRow(inModel, tranCodeModel);
        }
    }

    /**
     * 构建主流水信息
     * @param inModel inModel
     * @param tranCodeModel tranCodeModel
     * @return TaeAcctMaintradesRow
     */
   /* private TaeAcctMaintradesRow buildTranMainRow(In inModel, TranCodeModel tranCodeModel) {
        AcctTransactionInModel model = (AcctTransactionInModel) inModel;
        //获取现金交易标识
        String cashFlag = BusiUtil.isEqualY(model.getRbTranDef().getCashTranFlag()) ? "0" : "1";
        return GenerateTaeTradesUtil.createMainRow(
                Context.getInstance(), model.getTranAmt(), BaseEvent.DEBT.toString(), tranCodeModel.getTranCodeDesc(), cashFlag);
    }
*/

    /**
     * 构建 TranCodeModel
     * @param inModel inModel
     * @return TranCodeModel
     */
    private TranCodeModel buildTranCodeModel(In inModel) {

        TranCodeModel tranCodeModel = new TranCodeModel();
        // 初始化TranCodeModel
        rbTranCodeOperate.initTranCodeModel(inModel, tranCodeModel);
        // 添加扩展执行项
        List<RbTranProcessModel> extendProcessModelList = extendProcess(inModel);
        tranCodeModel.getTranProcessModels().addAll(extendProcessModelList);
        // 添加扩展检查项
        List<RbTranCheckModel> extendCheckModelList  = extendCheck(inModel);
        tranCodeModel.getTranCheckModels().addAll(extendCheckModelList);

        return tranCodeModel;
    }

    public void execTae() {
        SysHead sysHead = Context.getInstance().getSysHead();
        SettleEngineOut settleEngineOut = new SettleEngineOut();
        // 调用TAE
        SettleEngineIn settleEngineIn = new SettleEngineIn(maintradesRow, subtradesRows);
        if (BusiUtil.isEquals(sysHead.getTmnIdNo(), "TCC")) {
            log.info("tcc ------> settleEngineIn: {}", settleEngineIn);
            settleEngineOut = TaeRpc.transferTcc(settleEngineIn);
        } else {
            settleEngineOut = TaeRpc.transactionFoOrder(settleEngineIn);
        }
        // TODO 好像不需要判断，无论成功都需要清空
        if (null != settleEngineOut && "000000".equals(settleEngineOut.getRetCode())) {
            maintradesRow = null;
            subtradesRows.clear();
        }
    }
    /**
     * 获取扩展执行项
     * @param inModel inModel
     * @return List
     */
    public List<RbTranProcessModel> extendProcess(In inModel) { return new ArrayList<>(); }

    /**
     * 获取扩展检查项目
     * @param inModel inModel
     * @return List
     */
    public List<RbTranCheckModel> extendCheck(In inModel) { return new ArrayList<>(); }

    /**
     * 执行业务扩展执行内容
     * @param inModel inModel
     */
    public void process(In inModel) {}

    /**
     * 执行业务扩展检查内容
     * @param inModel inModel
     */
    public void check(In inModel) {}

    /**
     * 初始化TAE主流水
     * @param inModel inModel
     * @param tranCodeModel tranCodeModel
     * @return RbAcctTransactionOutModel
     */
    public abstract void initTaeRow(In inModel, TranCodeModel tranCodeModel);

    /**
     * 构建交易参数入参模型
     * @param request request
     */
    public abstract void buildTransactionInModel(EnsRequest request);

}
