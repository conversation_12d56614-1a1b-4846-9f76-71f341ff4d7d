package com.dcits.ensemble.rb.business.dbmodel;

import com.dcits.ensemble.dbmanage.dbmodel.EnsBaseDbBean;
import com.dcits.ensemble.rb.business.model.cm.common.BaseModel;
import lombok.Data;

import java.util.Date;

@Data
public class RbTranCheck extends EnsBaseDbBean {

    /** 交易分类 */
    private String tranId;
    /** 执行业务编码 */
    private String processId ;
    /** 交易分类 */
    private String tranCode ;
    /** 属性ID */
    private String attrId ;
    /** 属性类型 */
    private String attrType ;
    /** 属性规则 */
    private String attrRule ;
    /** 属性值 */
    private String attrValue ;
    /** 序号 */
    private String seqNo ;
    /** 借贷标识 */
    private String crDrInd ;
    /** 校验标识 */
    private String checkInd ;
    /** 现金交易标识 */
    private String cashTranFlag ;
    /** 尾箱更新标识 */
    private String updTailboxFlag ;
    /** 状态 */
    private String status ;
    /** 交易时间 */
    private Date tranDate ;
    /** 最后修改时间 */
    private Date lastChangeDate ;
    /** 最后修改柜员 */
    private String lastChangeUserId ;
    /** 法人 */
    private String company ;
    /** 交易时间戳 */
    private String tranTimestamp ;
}
