package com.dcits.ensemble.rb.business.dbmodel;

import com.dcits.ensemble.dbmanage.dbmodel.EnsBaseDbBean;
import lombok.Data;

import java.util.Date;

@Data
public class RbTranProcess extends EnsBaseDbBean {


    /**
     * 交易时间戳
     */
    private String tranTimestamp;

    /**
     * 交易日期
     */
    private Date tranDate;
    /**
     * 最后修改柜员
     */
    private String lastChangeUserId;

    /**
     * 处置描述
     */
    private String processDesc;

    /**
     * 交易ID
     */
    private String tranId;

    /**
     * 最后修改日期
     */
    private Date lastChangeDate;

    /**
     * 序号
     */
    private String seqNo;

    /**
     * 流程总ID
     */
    private String processId;

    /**
     * 法人
     */
    private String company;

    /**
     * 传输代码
     */
    private String tranCode;
}
