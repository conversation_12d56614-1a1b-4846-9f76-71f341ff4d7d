package com.dcits.ensemble.rb.business.dbmodel;

import com.dcits.ensemble.dbmanage.dbmodel.EnsBaseDbBean;
import lombok.Data;

import java.util.Date;

@Data
public class RbTranCode extends EnsBaseDbBean {

    /**
     * 交易ID
     */
    private String tranId;
    /**
     * 流程ID
     */
    private String flowId;
    /**
     * 交易分类
     */
    private String tranCode;
    /**
     * 交易分类描述
     */
    private String tranCodeDesc;
    /**
     * 渠道
     */
    private String channel;
    /**
     * 交易时间
     */
    private Date tranDate;
    /**
     * 最后修改时间
     */
    private Date lastChangeDate;

    /**
     * 最后修改柜员
     */
    private String lastChangeUserId;
    /**
     * 法人
     */
    private String company;
    /**
     * 交易时间戳
     */
    private String tranTimestamp;
}
