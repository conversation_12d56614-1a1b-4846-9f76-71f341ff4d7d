package com.dcits.ensemble.rb.business.bc.component.acct.tran.process;

import com.dcits.comet.util.json.JSONUtil;
import com.dcits.ensemble.rb.business.api.component.acct.tran.IRbBusiTranProcess;
import com.dcits.ensemble.rb.business.common.util.tae.GenerateTaeTradesUtil;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementCsl;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbTranDef;
import com.dcits.ensemble.rb.business.model.agr.AgreementTypeEnum;
import com.dcits.ensemble.rb.business.model.cm.BaseEventModel;
import com.dcits.ensemble.rb.business.model.cm.common.RbAcctStandardModel;
import com.dcits.ensemble.rb.business.model.cm.common.TranCodeDict;
import com.dcits.ensemble.rb.business.model.cm.restful.rb.AsynNormalCurrentCretIn;
import com.dcits.ensemble.rb.business.model.cm.restful.rb.AsynNormalCurrentDebtIn;
import com.dcits.ensemble.rb.business.model.cm.restful.tae.TaeAcctMaintradesRow;
import com.dcits.ensemble.rb.business.model.cm.restful.tae.TaeAcctSubtradesRow;
import com.dcits.ensemble.rb.business.model.cm.transaction.bal.AcctTransactionInModel;
import com.dcits.ensemble.rb.business.repository.agr.RbAgreementCslRepository;
import com.dcits.ensemble.util.BusiUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@Service
@Slf4j
public class BusiNormalCurrentDebtProcess implements IRbBusiTranProcess {

    @Resource
    private RbAgreementCslRepository rbAgreementCslRepository;

    @Override
    public TranCodeDict getProcess() {
        return TranCodeDict.NORMAL_CURRENT_DEBT;
    }

    @Override
    public List<TaeAcctSubtradesRow> process(BaseEventModel inModel, TaeAcctMaintradesRow maintradesRow) {
        List<TaeAcctSubtradesRow> subtradesRowList = new ArrayList<>();
        // 获取账户交易入参模型
        AcctTransactionInModel model = (AcctTransactionInModel) inModel;
        // 获取账户模型
        RbAcctStandardModel acctStandardModel = model.getRbAcctStandardModel();
        // 获取交易类型定义
        RbTranDef rbTranDef = model.getRbTranDef();

        //查询联动扣款协议信息
        RbAgreementCsl rbAgreementCsl = rbAgreementCslRepository.selectByCondition(model.getBaseAcctNo(),model.getProdType(),model.getAcctCcy(),"A");
        boolean isSigned = BusiUtil.isNotNull(rbAgreementCsl);
        // 协议类型为CSLA但交易类型不为支票支取，走普通扣款分支
        if (!isSigned || BusiUtil.isEquals(AgreementTypeEnum.CSLA.name(), rbAgreementCsl.getAgreementType())) {

            // 设置备注内容
            if (BusiUtil.isNull(model.getNarrative())) {
                model.setNarrative(rbTranDef.getTranTypeDesc());
            }
            // 构建借方流水d对象
            AsynNormalCurrentDebtIn asynNormalCurrentDebtIn = new AsynNormalCurrentDebtIn(model);
            String jsonDebtData = JSONUtil.toJsonStr(asynNormalCurrentDebtIn);

            //组织借方子流水
            TaeAcctSubtradesRow subDebtRow = GenerateTaeTradesUtil.createRbSingleSubRow(
                    maintradesRow.getMainId(), model.getTranAmt(), acctStandardModel, model.getRbTranDef(), TranCodeDict.NORMAL_CURRENT_DEBT.toString()
                    , jsonDebtData);
            subtradesRowList.add(subDebtRow);
        }

        return subtradesRowList;
    }

    @Override
    public BaseEventModel buildModel(BaseEventModel inModel) {
        // 获取账户交易入参模型
        AcctTransactionInModel model = (AcctTransactionInModel) inModel;

        AcctTransactionInModel acctTransactionInModelDebt = model;

        return acctTransactionInModelDebt;
    }


}
