package com.dcits.ensemble.rb.business.bc.component.acct.tran.check;

import com.dcits.ensemble.rb.business.api.component.acct.tran.IRbBusiTranCheck;
import com.dcits.ensemble.rb.business.bc.component.cm.common.RbClientCheckComponent;
import com.dcits.ensemble.rb.business.model.acct.CheckTypeEnum;
import com.dcits.ensemble.rb.business.model.acct.RbTranCheckModel;
import com.dcits.ensemble.rb.business.model.cm.BaseEventModel;
import com.dcits.ensemble.rb.business.model.cm.common.RbAcctStandardModel;
import com.dcits.ensemble.rb.business.model.cm.transaction.bal.AcctTransactionInModel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
@Slf4j
public class BusiClientBasisCheck implements IRbBusiTranCheck {

    @Resource
    private RbClientCheckComponent rbClientCheckComponent;

    @Override
    public CheckTypeEnum getCheckClass() {
        return CheckTypeEnum.CLIENT_BASIS;
    }

    @Override
    public void check(BaseEventModel inModel, RbTranCheckModel checkModel) {
        AcctTransactionInModel model = (AcctTransactionInModel) inModel;

        RbAcctStandardModel acctStdModel = model.getRbAcctStandardModel();
        log.info("[BusiClientBasisCheck.check]--> Basic customer inspection begins");
        log.debug("acctStdModel info is {}", acctStdModel.getAcctInfoString());
        rbClientCheckComponent.checkClientCommon(acctStdModel.getClientNo());
        log.info("[BusiClientBasisCheck.check]--> Customer basic inspection is over");

    }
}
