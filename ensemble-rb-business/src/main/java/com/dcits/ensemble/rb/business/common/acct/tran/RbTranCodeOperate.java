package com.dcits.ensemble.rb.business.common.acct.tran;

import com.dcits.comet.commons.Context;
import com.dcits.comet.commons.utils.BeanUtil;
import com.dcits.ensemble.rb.business.dbmodel.RbTranCheck;
import com.dcits.ensemble.rb.business.dbmodel.RbTranCode;
import com.dcits.ensemble.rb.business.dbmodel.RbTranProcess;
import com.dcits.ensemble.rb.business.model.acct.RbTranCheckModel;
import com.dcits.ensemble.rb.business.model.acct.RbTranProcessModel;
import com.dcits.ensemble.rb.business.model.acct.TranCodeModel;
import com.dcits.ensemble.rb.business.model.cm.BaseEventModel;
import com.dcits.ensemble.rb.business.repository.acct.RbTranCheckRepository;
import com.dcits.ensemble.rb.business.repository.acct.RbTranCodeRepository;
import com.dcits.ensemble.rb.business.repository.acct.RbTranProcessRepository;
import com.dcits.ensemble.util.BusiUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
public class RbTranCodeOperate {

    @Autowired
    private RbTranCodeRepository tranCodeRepository;
    @Autowired
    private RbTranProcessRepository tranProcessRepository;
    @Autowired
    private RbTranCheckRepository tranCheckRepository;

    /**
     * 初始化TranCodeModel
     * @param inModel inModel
     * @param tranCodeModel tranCodeModel
     */
    public void initTranCodeModel(BaseEventModel inModel, TranCodeModel tranCodeModel) {

        if (BusiUtil.isNull(inModel.getTranCode()) || BusiUtil.isNull(inModel.getFlowId())) {
            // TODO 交易码/流程ID不能为kon
            throw  BusiUtil.createBusinessException("");
        }

        String channel = Context.getInstance().getSourceType();
        String company = Context.getInstance().getCompany();

        RbTranCode rbTranCode = tranCodeRepository.selectTranCodeInfo(inModel.getFlowId(), inModel.getTranCode(), channel, company);
        if (BusiUtil.isNotNull(rbTranCode)) {

            BeanUtil.copy(rbTranCode, tranCodeModel);
            // 构建检查项
            List<RbTranCheckModel> rbTranCheckModels = this.getTranCheckModel(rbTranCode);
            tranCodeModel.setTranCheckModels(BusiUtil.nvl(rbTranCheckModels, new ArrayList<>()));
            // 构建执行项
            List<RbTranProcessModel> rbTranProcessModels = this.getTranProcessModel(rbTranCode);
            tranCodeModel.setTranProcessModels(BusiUtil.nvl(rbTranProcessModels, new ArrayList<>()));

        } else {
            // TODO 交易码/流程ID不能为kon
            throw  BusiUtil.createBusinessException("");
        }
    }

    /**
     * 获取参数配置的交易执行项
     * @param rbTranCode rbTranCode
     * @return List
     */
    private List<RbTranProcessModel> getTranProcessModel(RbTranCode rbTranCode) {
        List<RbTranProcessModel> rbTranProcessModels = new ArrayList<>();

        RbTranProcess rbTranProcess = new RbTranProcess();
        rbTranProcess.setTranId(rbTranCode.getTranId());
        rbTranProcess.setTranCode(rbTranCode.getTranCode());
        rbTranProcess.setCompany(Context.getInstance().getCompany());
        List<RbTranProcess> rbTranProcessList = tranProcessRepository.selectTranProcessList(rbTranProcess);
        if (BusiUtil.isNotNull(rbTranProcessList)) {
            BeanUtil.listCopy(rbTranProcessList, rbTranProcessModels, RbTranProcessModel.class);
        }
        return rbTranProcessModels;
    }


    /**
     * 获取参数配置的交易检查项
     * @param rbTranCode rbTranCode
     * @return List
     */
    private List<RbTranCheckModel> getTranCheckModel(RbTranCode rbTranCode) {
        List<RbTranCheckModel> rbTranCheckModels = new ArrayList<>();

        RbTranCheck rbTranCheck = new RbTranCheck();
        rbTranCheck.setTranId(rbTranCode.getTranId());
        rbTranCheck.setTranCode(rbTranCode.getTranCode());
        rbTranCheck.setCompany(Context.getInstance().getCompany());
        rbTranCheck.setStatus("A");
        List<RbTranCheck> rbTranChecks = tranCheckRepository.selectTranCheckList(rbTranCheck);
        if (BusiUtil.isNotNull(rbTranChecks)) {
            BeanUtil.listCopy(rbTranChecks, rbTranCheckModels, RbTranCheckModel.class);
        }
        return rbTranCheckModels;
    }

}
