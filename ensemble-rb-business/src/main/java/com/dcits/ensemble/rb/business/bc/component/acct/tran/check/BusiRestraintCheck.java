package com.dcits.ensemble.rb.business.bc.component.acct.tran.check;

import com.dcits.comet.commons.Context;
import com.dcits.comet.commons.utils.DateUtil;
import com.dcits.comet.rpc.api.model.head.Result;
import com.dcits.ensemble.fm.model.FmChannel;
import com.dcits.ensemble.rb.business.api.component.acct.tran.IRbBusiTranCheck;
import com.dcits.ensemble.rb.business.api.unit.res.IRestraintsCheck;
import com.dcits.ensemble.rb.business.api.unit.res.RestraintsCheckFactory;
import com.dcits.ensemble.rb.business.bc.unit.acct.business.AcctStatusEnum;
import com.dcits.ensemble.rb.business.bc.unit.acct.transaction.business.TransactionUtil;
import com.dcits.ensemble.rb.business.common.constant.BaseEvent;
import com.dcits.ensemble.rb.business.common.constant.ClientTypeEnum;
import com.dcits.ensemble.rb.business.common.constant.RbDictEnum;
import com.dcits.ensemble.rb.business.common.rpc.CifRpc;
import com.dcits.ensemble.rb.business.common.util.PreconditionUtils;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbChannelControl;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbControlTranRelation;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbControlTypeDef;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbTranDef;
import com.dcits.ensemble.rb.business.model.acct.CheckTypeEnum;
import com.dcits.ensemble.rb.business.model.acct.RbTranCheckModel;
import com.dcits.ensemble.rb.business.model.cm.BaseEventModel;
import com.dcits.ensemble.rb.business.model.cm.common.RbAcctStandardModel;
import com.dcits.ensemble.rb.business.model.cm.restful.cif.CifClientControlQueryOut;
import com.dcits.ensemble.rb.business.model.cm.transaction.bal.AcctTransactionInModel;
import com.dcits.ensemble.rb.business.model.interest.model.RbBusiEnum;
import com.dcits.ensemble.rb.business.model.res.control.ForbidChannelsEnum;
import com.dcits.ensemble.rb.business.model.res.restraints.CheckRestraintsModel;
import com.dcits.ensemble.rb.business.model.res.restraints.ResCheckControlModel;
import com.dcits.ensemble.rb.business.model.res.restraints.RestraintsTypeEnum;
import com.dcits.ensemble.rb.business.repository.res.control.FmChannelRepository;
import com.dcits.ensemble.rb.business.repository.res.control.RbChannelControlRepository;
import com.dcits.ensemble.rb.business.repository.res.control.RbControlTranRelationRepository;
import com.dcits.ensemble.rb.business.repository.res.control.RbControlTypeDefRepository;
import com.dcits.ensemble.util.BusiUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * busi restraint check
 */
@Service
@Slf4j
public class BusiRestraintCheck implements IRbBusiTranCheck {

    @Resource
    private RbChannelControlRepository rbChannelControlRepository;
    @Resource
    private RbControlTranRelationRepository rbControlTranRelationRepository;
    @Resource
    private RbControlTypeDefRepository controlTypeDefRepository;
    @Resource
    private FmChannelRepository fmChannelRepository;


    @Override
    public CheckTypeEnum getCheckClass() {
        return CheckTypeEnum.RESTRAINT;
    }

    @Override
    public void check(BaseEventModel inModel, RbTranCheckModel checkModel) {
        AcctTransactionInModel model = (AcctTransactionInModel) inModel;

        String tranType = model.getTranType();
        RbAcctStandardModel acctStdModel = model.getRbAcctStandardModel();
        log.info("[BusiRestraintCheck. check]--> Household restriction inspection begins");
        //账户确认信息为空，则返回不检查限制
        if(BusiUtil.isNull(acctStdModel)){
            return;
        }

        log.debug("acctStdModel info is {}", acctStdModel.getAcctInfoString());
        log.debug("tranType is {}", tranType);
        String effectDate = BusiUtil.nvl(DateUtil.formatDate(acctStdModel.getEffectDate()), Context.getInstance().getRunDate());

        PreconditionUtils.checkNotNull(acctStdModel, RbDictEnum.ACCTCONFIRM);
        RbTranDef rbTranDef;
        if (BusiUtil.isNotNull(tranType)) {
            rbTranDef = TransactionUtil.getMbTranDef(tranType);
        } else {
            rbTranDef = new RbTranDef();
            rbTranDef.setCrDrInd("1");
            rbTranDef.setCashTranFlag("1");
        }
        ResCheckControlModel resCheckControlModel = new ResCheckControlModel();
        resCheckControlModel.setLostType(null);
        CheckRestraintsModel checkRestraintsModel = new CheckRestraintsModel(acctStdModel);
        checkRestraintsModel.setEffectDate(DateUtil.parseDate(effectDate));
        checkRestraintsModel.setRbTranDef(rbTranDef);
        IRestraintsCheck restraintsCheck = RestraintsCheckFactory.getRestraintsBusiness(RestraintsTypeEnum.ALL);
        List<Result> errorList = restraintsCheck.check(checkRestraintsModel, resCheckControlModel);
        if (BusiUtil.isNotNull(errorList)) {
            throw BusiUtil.createBusinessException(new ArrayList<>(errorList));
        }

        // 检查渠道控制
        this.checkChannelControl(acctStdModel, rbTranDef);

        //BUG_021144 限制除了个人账户转入外，其余的久悬账户不可交易
        if(BusiUtil.isEquals(AcctStatusEnum.SUSPENSE.getCode(), acctStdModel.getAcctStatus())
                && (BusiUtil.isNotEquals(rbTranDef.getCrDrInd(), BaseEvent.CRET.toString())
                && BusiUtil.isNotEquals(ClientTypeEnum.CLIENT_TYPE_PERSONAL.getCode(),acctStdModel.getClientType()))){
            throw BusiUtil.createBusinessException("RB7003", acctStdModel.getBaseAcctNo(), acctStdModel.getAcctType(), acctStdModel.getAcctCcy(), acctStdModel.getAcctSeqNo());
        }

        log.info("[BusiRestraintCheck. check]--> Household restriction inspection is over.");

    }

    /**
     * 检查渠道控制
     *
     * @param acctStdModel 账户
     * @param rbTranDef    交易类型
     */
    public void checkChannelControl(RbAcctStandardModel acctStdModel, RbTranDef rbTranDef) {
        Map<String, Object> checkMap = new HashMap<>();
        checkMap.put("tranType", rbTranDef.getTranType());
        checkMap.put("sourceType", Context.getInstance().getSourceType());
        checkMap.put("messageType", Context.getInstance().getMessageType());
        checkMap.put("messageCode", Context.getInstance().getMessageCode());
        checkMap.put("systemId", Context.getInstance().getSysHead().getSystemId());
        List<RbChannelControl> rbChannelControls = rbChannelControlRepository.selectEffectingControl(acctStdModel.getClientNo(), acctStdModel.getInternalKey());
        log.info("Channel controls under account {} include {}", acctStdModel.getBaseAcctNo(), rbChannelControls);
        for (RbChannelControl channelControl : rbChannelControls) {
            String controlType = channelControl.getControlType();
            List<RbControlTypeDef> rbControlTypeDefs = controlTypeDefRepository.selectListByControlType(controlType);
            //进行科目和渠道的匹配
            List<RbControlTypeDef> matchControl = rbControlTypeDefs.stream().
                    filter(c -> matchClass(c.getControlClass(), checkMap)).
                    filter(c -> matchChannel(c.getForbidChannels(), checkMap)).
                    collect(Collectors.toList());

            if (BusiUtil.isNotNull(matchControl)) {
                log.info("Currently matching controls are {}", matchControl);
                throw BusiUtil.createBusinessException("RB6524");
            }
        }

        // 客户渠道控制检查
        CifClientControlQueryOut cifControl = CifRpc.getCifControl(acctStdModel.getClientNo());
        cifControlCheck(cifControl, checkMap);
    }

    /**
     * 匹配控制科目
     *
     * @param controlClass 控制科目
     * @param checkDataMap 检查参数
     * @return 是否匹配
     */
    private boolean matchClass(String controlClass, Map<String, Object> checkDataMap) {
        String messageType = (String) checkDataMap.get("messageType");
        String messageCode = (String) checkDataMap.get("messageCode");
        String tranType = (String) checkDataMap.get("tranType");
        //放开查询类交易
        if (BusiUtil.isEquals(messageType, "1400")) {
            return false;
        }

        String serviceNo = messageType + "-" + messageCode;
        List<RbControlTranRelation> rbControlTranRelations = rbControlTranRelationRepository.selectListByClass(controlClass);
        return rbControlTranRelations.stream().anyMatch(p -> {
            //没有配置服务码则不校验
            if (BusiUtil.isNotEquals(p.getServiceNo(), RbBusiEnum.ALL)) {
                if (BusiUtil.isEquals(p.getServiceNo(), serviceNo)) {
                    return BusiUtil.isEquals(p.getTranType(), RbBusiEnum.ALL) || BusiUtil.isEquals(p.getTranType(), tranType);
                }
                return false;
            }
            return BusiUtil.isEquals(p.getTranType(), RbBusiEnum.ALL) || BusiUtil.isEquals(p.getTranType(), tranType);
        });
    }

    /**
     * 匹配渠道
     *
     * @param channels     渠道
     * @param checkDataMap 检查参数
     * @return 是否匹配
     */
    private boolean matchChannel(String channels, Map<String, Object> checkDataMap) {
        String sourceType = ((String) checkDataMap.get("sourceType")).toUpperCase();
        String[] splits = channels.split(",");
        List<String> channleQuerys = new ArrayList<>();
        List<FmChannel> forbidChannels = new ArrayList<>();
        for (String channel : splits) {
            //非柜面
            if (BusiUtil.isEquals(channel, ForbidChannelsEnum.NO_COUNTER.getValue())) {
                List<FmChannel> fmChannels = fmChannelRepository.selectUncounterChannelList();
                forbidChannels.addAll(fmChannels);
            } else {
                channleQuerys.add(channel);
            }
        }
        // 所有的禁止渠道
        forbidChannels.addAll(fmChannelRepository.selectChannelListByList(channleQuerys));
        // 当前渠道是否匹配
        return forbidChannels.stream().map(FmChannel::getChannel).anyMatch(channel -> BusiUtil.isEquals(sourceType, channel));
    }

    /**
     * 客户渠道控制检查
     *
     * @param cifControl 客户控制信息
     * @param checkMap   检查参数
     */
    private void cifControlCheck(CifClientControlQueryOut cifControl, Map<String, Object> checkMap) {
        log.info("Customer {} channel control verification starts", cifControl.getClientNo());
        List<CifClientControlQueryOut.CtrlArray> cifControlCtrlArray = cifControl.getCtrlArray();
        cifControlCtrlArray = cifControlCtrlArray.stream().filter(ctrl -> BusiUtil.isEquals(ctrl.getControlStatus(), "A")).collect(Collectors.toList());
        if (BusiUtil.isNull(cifControlCtrlArray)) {
            return;
        }
        for (CifClientControlQueryOut.CtrlArray ctrlArray : cifControlCtrlArray) {
            String controlType = ctrlArray.getControlType();
            List<RbControlTypeDef> rbControlTypeDefs = controlTypeDefRepository.selectListByControlType(controlType);
            //进行科目和渠道的匹配
            List<RbControlTypeDef> matchControl = rbControlTypeDefs.stream().
                    filter(c -> matchClass(c.getControlClass(), checkMap)).
                    filter(c -> matchChannel(c.getForbidChannels(), checkMap)).
                    collect(Collectors.toList());

            if (BusiUtil.isNotNull(matchControl)) {
                log.info("Currently matching controls are {}", matchControl);
                throw BusiUtil.createBusinessException("RB6526");//NOSONAR
            }

        }
    }
}
