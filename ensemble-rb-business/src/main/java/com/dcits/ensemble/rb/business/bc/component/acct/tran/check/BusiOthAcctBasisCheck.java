package com.dcits.ensemble.rb.business.bc.component.acct.tran.check;

import com.dcits.comet.flow.BusinessParamContext;
import com.dcits.ensemble.rb.business.api.component.acct.tran.IRbBusiTranCheck;
import com.dcits.ensemble.rb.business.bc.component.acct.RbAcctCheckComponent;
import com.dcits.ensemble.rb.business.model.acct.CheckTypeEnum;
import com.dcits.ensemble.rb.business.model.acct.RbTranCheckModel;
import com.dcits.ensemble.rb.business.model.cm.BaseEventModel;
import com.dcits.ensemble.rb.business.model.cm.common.RbAcctStandardModel;
import com.dcits.ensemble.rb.business.model.cm.transaction.bal.AcctTransactionInModel;
import com.dcits.ensemble.util.BusiUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
@Slf4j
public class BusiOthAcctBasisCheck implements IRbBusiTranCheck {

    @Resource
    private RbAcctCheckComponent rbAcctCheckComponent;

    @Override
    public CheckTypeEnum getCheckClass() {
        return CheckTypeEnum.OTH_ACCT_BASIS;
    }

    @Override
    public void check(BaseEventModel inModel, RbTranCheckModel checkModel) {
        AcctTransactionInModel model = (AcctTransactionInModel) inModel;

        RbAcctStandardModel othAcctStdModel = model.getOthRbAcctStandardModel();
        log.info("[BusiAcctBasisCheck.check]--> Basic account verification begins");
        log.debug("acctStdModel info is {}", othAcctStdModel.getAcctInfoString());
        if (BusiUtil.isEqualY((String) BusinessParamContext.getInstance().getValue("openFlag"))){
            return;
        }

        rbAcctCheckComponent.checkAcctCommon(othAcctStdModel);
        String individual = othAcctStdModel.getIsIndividual();
        if (BusiUtil.isEqualY(individual)) {
            rbAcctCheckComponent.checkIndAcct(othAcctStdModel);
        }
        if (BusiUtil.isEqualN(individual)) {
            rbAcctCheckComponent.checkCorpAcct(othAcctStdModel);
        }
        log.info("[BusiAcctBasisCheck.check]--> Account basic verification ended");
    }
}
