package com.dcits.ensemble.rb.business.api.component.acct.tran;

import com.dcits.ensemble.rb.business.model.cm.BaseEventModel;
import com.dcits.ensemble.rb.business.model.cm.common.TranCodeDict;
import com.dcits.ensemble.rb.business.model.cm.restful.tae.TaeAcctMaintradesRow;
import com.dcits.ensemble.rb.business.model.cm.restful.tae.TaeAcctSubtradesRow;

import java.util.List;

/**
 * The interface Rb busi tran process
 */
public interface IRbBusiTranProcess {

    /**
     * get process type
     * @return process typr
     */
    TranCodeDict getProcess();

    /**
     * process
     * @param inModel inModel
     * @return List
     */
    List<TaeAcctSubtradesRow> process(BaseEventModel inModel, TaeAcctMaintradesRow maintradesRow);


    /**
     * 构建执行模型
     * @param inModel inModel
     * @return BaseEventModel
     */
    BaseEventModel buildModel(BaseEventModel inModel);
}
