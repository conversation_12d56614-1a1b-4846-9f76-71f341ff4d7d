package com.dcits.ensemble.rb.business.repository.acct;

import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.rb.business.dbmodel.RbTranCheck;
import com.dcits.ensemble.repository.BusinessRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@BusiUnit
@Slf4j
public class RbTranCheckRepository extends BusinessRepository {

    public RbTranCheck selectTranCheckInfo(String tranId, String processId, String tranCode, String attrId, String company) {
        Map<String, Object> param = new HashMap<>(16);
        param.put("processId", processId);
        param.put("tranCode", tranCode);
        param.put("tranId",tranId);
        param.put("attrId",attrId);
        param.put("company",company);
        return daoSupport.selectOne(RbTranCheck.class.getName() + ".selectOne",  param);
    }

    public List<RbTranCheck> selectTranCheckList(RbTranCheck rbTranCheck) {
        Map<String, Object> param = new HashMap<>(16);
        param.put("processId", rbTranCheck.getProcessId());
        param.put("tranCode", rbTranCheck.getTranCode());
        param.put("tranId",rbTranCheck.getTranId());
        param.put("attrId",rbTranCheck.getAttrId());
        param.put("company",rbTranCheck.getCompany());
        param.put("status",rbTranCheck.getStatus());
        return daoSupport.selectList(RbTranCheck.class.getName() + ".selectList",  param);
    }


}
